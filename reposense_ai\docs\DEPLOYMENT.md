# RepoSense AI - Deployment Guide

## 🎯 Deployment Options

RepoSense AI supports two deployment approaches:

1. **Development Deployment** - Build from source for development and testing
2. **Production Deployment** - Use pre-built Docker images for production

## 🚀 Development Deployment (Source-Based)

### Development Prerequisites

- Docker and Docker Compose installed
- Source code access (SVN/Git repository)

### Deploy from Source

```bash
# Get the source code
svn checkout <your-svn-url> reposense_ai
# OR
git clone <your-repo-url> reposense_ai

cd reposense_ai

# Start RepoSense AI (builds from source)
docker-compose up -d

# Access web interface
open http://localhost:5001
```

**Perfect for:** Development, testing, customization, and debugging.

## 🐳 Production Deployment (Docker Image)

### Production Prerequisites

- Docker and Docker Compose installed
- Pre-built Docker image (see Building Images section)

### Deploy with Pre-built Image

```bash
# Create deployment directory
mkdir reposense_ai && cd reposense_ai

# Create docker-compose.yml (see Production Docker Compose section)
# Load the Docker image
docker load -i reposense-ai.tar

# Start RepoSense AI
docker-compose up -d

# Access web interface
open http://localhost:5001
```

**Perfect for:** Production servers, consistent deployments, and faster startup.

## 🏗️ Building Docker Images (Windows)

### Build Scripts

RepoSense AI includes Windows-compatible build scripts:

#### PowerShell Script (Recommended)

```powershell
# Build image locally
.\build-for-deployment.ps1

# Build and save to file for transfer
.\build-for-deployment.ps1 -Save "reposense-ai.tar"

# Build and deploy directly to remote server
.\build-for-deployment.ps1 -Deploy "*************"

# Build with custom tag
.\build-for-deployment.ps1 -Tag "v1.0.0"
```

#### Batch Script (Simple)

```cmd
# Build image locally
build-for-deployment.bat

# Build and save to file
build-for-deployment.bat --save reposense-ai.tar

# Build with custom tag
build-for-deployment.bat --tag v1.0.0
```

### Manual Docker Build

```bash
# Build from source directory
cd reposense_ai
docker build -t reposense-ai:latest .

# Save image for transfer
docker save reposense-ai:latest -o reposense-ai.tar
```

## 🔧 Configuration

### Primary Configuration Method

1. **Visit** <http://localhost:5001>
2. **Click** "Configuration" in the navigation
3. **Configure** your settings:
   - Ollama Host (e.g., `http://your-ollama-server:11434`)
   - Ollama Model (e.g., `granite3.3:8b`, `qwen3-coder:latest`)
   - Repository settings (SVN/Git URLs, credentials)
   - Email notifications
   - Check intervals
4. **Click** "Save Configuration"

### Development Environment Overrides

For development mode, create a `.env` file:

```bash
# Copy the example (development deployment only)
cp .env.example .env

# Edit .env and uncomment needed overrides:
# OLLAMA_BASE_URL=http://your-ollama-server:11434
# OLLAMA_MODEL=granite3.3:8b
# For development mode, uncomment:
# REPOSENSE_AI_LOG_LEVEL=DEBUG
# REPOSENSE_AI_DB_DEBUG=true
# FLASK_DEBUG=1
```

## 🚀 Remote Server Deployment

### Transfer Methods

#### Option 1: File Transfer (Recommended)

```bash
# 1. Build and save image (on Windows development machine)
.\build-for-deployment.ps1 -Save "reposense-ai.tar"

# 2. Transfer to server (using SCP or WinSCP)
scp reposense-ai.tar user@server:/path/to/deployment/

# 3. SSH to server and deploy
ssh user@server
cd /path/to/deployment
docker load -i reposense-ai.tar
docker-compose up -d reposense-ai
```

#### Option 2: Direct Deployment (PowerShell)

```powershell
# Build and deploy in one command (requires SSH/SCP configured)
.\build-for-deployment.ps1 -Deploy "*************"
```

### Production Docker Compose

Create this `docker-compose.yml` on your production server:

```yaml
services:
  reposense-ai:
    image: reposense-ai:latest          # Use pre-built image
    container_name: reposense-ai
    ports:
      - "5001:5001"
    volumes:
      - ./data:/app/data               # Persistent data
      - ./logs:/app/logs               # Application logs
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
      - PYTHONUNBUFFERED=1
    networks:
      - ollama-network                 # Connect to existing Ollama network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - ollama

networks:
  ollama-network:
    external: true                     # Use existing network
```

### Production Considerations

#### 1. Directory Structure

The application automatically creates required directories:

```text
/path/to/deployment/
├── docker-compose.yml              # Production compose file
├── data/                           # Created by container
│   ├── config.json                # Application configuration
│   ├── documents.db               # Database
│   ├── output/                    # Generated reports
│   └── cache/                     # Temporary files
└── logs/                          # Created by container
    └── reposense_ai.log           # Application logs
```

#### 2. Permissions (Linux/Unix servers)

```bash
# Fix permissions if needed (container runs as uid 1000)
sudo chown -R 1000:1000 data logs
sudo chmod -R 755 data logs
```

#### 3. Integration with Existing Infrastructure

```yaml
# Add to existing docker-compose.yml with Ollama, Open WebUI, etc.
services:
  # ... existing services (ollama, open-webui, etc.)

  reposense-ai:
    image: reposense-ai:latest
    # ... configuration as above
```

#### 4. Reverse Proxy (Optional)

For HTTPS and custom domains:

```nginx
# nginx example
server {
    listen 80;
    server_name reposense.yourcompany.com;

    location / {
        proxy_pass http://localhost:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 5. Backup Strategy

```bash
# Backup data and configuration
tar -czf reposense-backup-$(date +%Y%m%d).tar.gz data/ logs/

# Restore data
tar -xzf reposense-backup-YYYYMMDD.tar.gz
```

## 🛠️ Management Commands

### Development Deployment Commands

```bash
# Start RepoSense AI (builds from source)
docker-compose up -d

# Stop RepoSense AI
docker-compose down

# View logs
docker-compose logs -f reposense-ai

# Rebuild after code changes
docker-compose build reposense-ai
docker-compose up -d reposense-ai

# Development mode (with debug)
cp .env.example .env && docker-compose up -d

# Check status
docker-compose ps

# Health check
curl http://localhost:5001/health
```

### Production Deployment Commands

```bash
# Start RepoSense AI (uses pre-built image)
docker-compose up -d reposense-ai

# Stop RepoSense AI
docker-compose stop reposense-ai

# View logs
docker-compose logs -f reposense-ai

# Update with new image
docker load -i new-reposense-ai.tar
docker-compose up -d reposense-ai

# Check status
docker-compose ps reposense-ai

# Health check
curl http://localhost:5001/health
```

### Image Management

```bash
# List RepoSense AI images
docker images reposense-ai

# Remove old images
docker image prune

# Tag image for versioning
docker tag reposense-ai:latest reposense-ai:v1.0.0

# Export image
docker save reposense-ai:latest -o reposense-ai-backup.tar
```

## 🔍 Troubleshooting

### Common Issues

#### Permission Issues (Linux/Unix servers)

```bash
# Check container logs for permission errors
docker-compose logs reposense-ai | grep -i permission

# Use automated permission fix scripts:

# For host-side permission issues (before container starts):
./scripts/fix-docker-permissions.sh

# For container-side permission issues (container running but has errors):
./scripts/fix-container-permissions.sh

# Manual fix (if scripts not available):
sudo chown -R 1000:1000 reposense_ai/data reposense_ai/logs
sudo chmod -R 755 reposense_ai/data reposense_ai/logs
docker-compose restart reposense-ai
```

#### Container Won't Start

```bash
# Check container status
docker-compose ps reposense-ai

# View detailed logs
docker-compose logs reposense-ai

# Check if image exists (production deployment)
docker images reposense-ai

# Rebuild image (development deployment)
docker-compose build reposense-ai
```

#### Ollama Connection Issues

```bash
# Test Ollama connectivity from container
docker-compose exec reposense-ai curl http://ollama:11434/api/tags

# Check if Ollama is running
docker-compose ps ollama

# Verify network connectivity
docker network ls
docker network inspect ollama-network
```

#### Web Interface Not Accessible

```bash
# Check if port is bound
netstat -tlnp | grep :5001
# or on Windows
netstat -an | findstr :5001

# Check container health
docker-compose exec reposense-ai curl http://localhost:5001/health

# Check firewall settings (production servers)
sudo ufw status
```

### Diagnostic Commands

#### Check Service Status

```bash
docker-compose ps reposense-ai
```

#### View Logs

```bash
# Recent logs
docker-compose logs --tail=50 reposense-ai

# Follow logs in real-time
docker-compose logs -f reposense-ai

# Search for errors
docker-compose logs reposense-ai | grep -i error
```

#### Health Check

```bash
curl http://localhost:5001/health
```

#### Container Information

```bash
# Get container details
docker inspect reposense-ai

# Check resource usage
docker stats reposense-ai

# Execute commands in container
docker-compose exec reposense-ai bash
```

### Reset and Recovery

#### Reset Configuration

```bash
# Stop service
docker-compose down

# Backup current config (optional)
cp data/config.json data/config.json.backup

# Remove configuration (will be recreated with defaults)
rm data/config.json

# Start service
docker-compose up -d

# Reconfigure via web interface
open http://localhost:5001
```

#### Complete Reset

```bash
# Stop service
docker-compose down

# Remove all data (WARNING: This deletes everything!)
rm -rf data/ logs/

# Start service (fresh installation)
docker-compose up -d
```

#### Update Deployment

```bash
# Development: Pull latest code and rebuild
svn update  # or git pull
docker-compose build reposense-ai
docker-compose up -d reposense-ai

# Production: Load new image
docker load -i new-reposense-ai.tar
docker-compose up -d reposense-ai
```

## 🎯 Deployment Comparison

### Development vs Production

| Aspect | Development Deployment | Production Deployment |
|--------|----------------------|---------------------|
| **Source** | Build from source code | Pre-built Docker image |
| **Build Time** | ~2-3 minutes | ~30 seconds (load image) |
| **Disk Space** | Source code + image | Image only |
| **Updates** | `docker-compose build` | Load new image |
| **Customization** | Full source access | Configuration only |
| **Debugging** | Full debugging capabilities | Limited to logs |
| **Use Case** | Development, testing | Production, staging |

### When to Use Each Method

#### Use Development Deployment When

- ✅ Developing or customizing RepoSense AI
- ✅ Testing new features or configurations
- ✅ Need to modify source code
- ✅ Debugging issues
- ✅ Learning how the system works

#### Use Production Deployment When

- ✅ Deploying to production servers
- ✅ Want consistent, tested deployments
- ✅ Need faster deployment times
- ✅ Limited server resources
- ✅ Multiple server deployments

## 🎉 Benefits of Current Approach

### Development Benefits

- **Full Control**: Access to all source code and configuration
- **Easy Debugging**: Full development environment
- **Rapid Iteration**: Quick rebuild and test cycles
- **Customization**: Modify any aspect of the application

### Production Benefits

- **Consistency**: Same image runs everywhere
- **Speed**: Fast deployment with pre-built images
- **Reliability**: Tested images reduce deployment risks
- **Efficiency**: Smaller footprint on production servers

### Shared Benefits

- **Simple Configuration**: Web interface for all settings
- **Automatic Setup**: Container handles directory creation and permissions
- **Health Monitoring**: Built-in health checks and diagnostics
- **Easy Maintenance**: Clear commands for common operations

## 🚀 Quick Start Summary

### For Development

```bash
# Get source and start
svn checkout <repo> reposense_ai && cd reposense_ai
docker-compose up -d
```

### For Production

```bash
# Load image and start
docker load -i reposense-ai.tar
docker-compose up -d reposense-ai
```

Both approaches provide the same powerful RepoSense AI functionality with the deployment method that best fits your needs! 🎯
