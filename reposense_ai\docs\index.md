# RepoSense AI Documentation

Welcome to the RepoSense AI documentation. This system provides automated monitoring of repositories with AI-powered documentation generation and plugin-based architecture.

## Table of Contents

### Getting Started

1. [System Overview](#system-overview)
2. [Deployment Guide](DEPLOYMENT.md) - Complete deployment guide for development and production
3. [Docker Guide](DOCKER_README.md) - Unified Docker setup
4. [Quick Configuration](quick-configuration.md) - Web interface configuration

### Core Documentation

1. [Features Overview](features.md) - Comprehensive feature documentation
2. [Configuration Reference](configuration.md) - Complete configuration options
3. [Architecture & Design](design.md) - System architecture and plugin development
4. [User Guide](usage.md) - How to use the system
5. [Integration Guide](integration.md) - External service integration

### Enterprise Features & Integration

1. [**Enterprise Features**](ENTERPRISE_FEATURES.md) - **Complete enterprise feature overview** (MASTER DOCUMENT)
2. [Enhanced Risk Assessment](ENHANCED_RISK_ASSESSMENT.md) - Advanced risk assessment with configurable aggressiveness levels
3. [Documentation Consolidation Summary](DOCUMENTATION_CONSOLIDATION_SUMMARY.md) - Consolidation details and organization
4. [Change Request Setup](CHANGE_REQUEST_SETUP.md) - SQL database integration for change management systems
5. [Change Request Testing](CHANGE_REQUEST_TESTING_INTEGRATION.md) - Testing framework and quality assurance for change request integration
6. [LDAP Integration Setup](ldap-integration.md) - Enterprise directory integration setup
7. [LDAP Testing Setup](LDAP_TESTING.md) - Development and testing setup for LDAP authentication
8. [Testing Framework](TESTING_FRAMEWORK.md) - Comprehensive testing framework documentation

### Development & Reference

1. [Development Guide](development.md) - Contributing, development setup, Windows support, and hot reload workflow
2. [Release Notes](release-notes.md) - Version history and architectural changes
3. [Changelog](CHANGELOG.md) - Detailed change log with recent enhancements

## System Overview

RepoSense AI is a comprehensive solution for monitoring repositories with AI-powered documentation generation and plugin-based architecture. The system supports SVN repositories with Git support planned for future releases.

### Key Features

#### Core Functionality

- **Plugin Architecture**: Extensible backend system supporting multiple repository types
- **AI Documentation**: Automatic commit analysis and documentation generation with Ollama
- **Document Management**: Browse, search, and manage generated documentation with professional PDF exports
- **Repository Discovery**: Advanced repository discovery with SSL support and protocol fallback
- **Modern Web Interface**: Responsive web UI with real-time monitoring and dual timestamp tracking
- **Enterprise Deployment**: Professional Windows-to-production deployment pipeline with Docker-first architecture
- **Web Interface Configuration**: Primary configuration via web interface with optional environment overrides

#### Enterprise Features (Latest)

- **LDAP Integration**: Complete enterprise directory integration with automatic user synchronization
- **Advanced Change Request Integration**: SQL database connectivity for change management systems with intelligent field mapping
  - **Smart Field Mapping**: Automatic database field mapping with comprehensive alias support for any database schema
  - **Multi-Database Support**: SplendidCRM, JIRA, ServiceNow, Azure DevOps, MySQL, PostgreSQL, and SQL Server integration
  - **Enhanced Pattern Recognition**: Configurable regex patterns for automatic change request detection in commit messages
  - **HTML Content Processing**: Intelligent HTML cleaning that preserves line breaks and formatting structure
  - **Comprehensive Documentation**: Complete field mapping reference guide with troubleshooting and best practices
- **Production-Ready Testing**: Comprehensive test suite with type safety and integration testing
- **Role-Based Access Control**: Granular permissions based on LDAP groups and organizational structure
- **Database Consolidation**: Single SQLite database with ACID compliance and automatic migration
- **Type Safety**: Full type annotation coverage with automated validation for production reliability

#### Enhanced Features (Latest)

- **Windows-Native Build Scripts**: Professional PowerShell and batch scripts for Docker image building
- **Production-Ready Deployment**: Pre-built Docker images with automated testing and health checks
- **Automated Permission Management**: Intelligent Docker volume permission handling with detailed diagnostics
- **Enhanced Error Reporting**: Clear, actionable error messages with multiple resolution options
- **Critical Stability Fixes**: Resolved metadata extraction failures and model availability issues
- **Robust LLM Integration**: Fixed MetadataExtractor initialization for proper specialized model support
- **Professional Document Downloads**: High-quality PDF and Markdown exports with syntax highlighting
- **AI Processing Transparency**: Complete visibility into AI model, processing time, and analysis results
- **Enhanced SVN Backend**: Comprehensive SSL certificate handling and multi-protocol support
- **Dual Timestamp Tracking**: Separate tracking for commit dates (from repository) and processing dates
- **Advanced Diff Visualization**: Color-coded unified diffs with proper syntax highlighting
- **Repository Branch Discovery**: Automatic detection of trunk, branches, and tags within repositories
- **Configuration Simplification**: Single config file with web-based management and environment overrides

#### Previous Features (v2.1.0)

- **User Feedback System**: Code review tracking, documentation quality ratings, and risk assessment overrides
- **Side-by-Side Diff Viewer**: Advanced diff visualization with format switching
- **Hybrid AI Analysis**: Fast heuristics with LLM fallback for robust metadata extraction
- **Multi-Encoding Support**: Handles UTF-8, Latin-1, CP1252, and binary files gracefully
- **Accurate Progress Tracking**: Fixed progress calculation for all revision ranges
- **On-Demand Diff Generation**: Efficient diff creation using stored repository metadata

### Architecture Highlights

- **Plugin-Based**: Repository backends as pluggable components
- **Service-Oriented**: Individual services for monitoring, user management, documents, etc.
- **Event-Driven**: Background monitoring with configurable intervals
- **Scalable**: Designed to handle multiple repositories and users efficiently
- **Extensible**: Clean architecture for adding new repository types and features

## Quick Start

1. **Clone and Setup**

   ```bash
   git clone <repository-url>
   cd reposense-ai
   ```

2. **Initialize Configuration**

   ```bash
   # Run setup script to create data/config.json
   ./setup.sh
   ```

3. **Start with Docker (Recommended)**

   ```bash
   # Single docker-compose file for all environments
   docker-compose up -d

   # Access web interface
   open http://localhost:5001
   ```

4. **Configure Repositories**
   - Open the web interface at <http://localhost:5001>
   - Navigate to Configuration tab
   - Add your repositories using the discovery feature or manually
   - Configure AI settings (Ollama model and host)
   - Start monitoring

5. **Download Professional Documents**
   - View any processed document
   - Use the download dropdown for PDF or Markdown export
   - All downloads include AI processing information and enhanced formatting

## System Requirements

- **Docker & Docker Compose** (recommended)
- **Python 3.8+** (for local development)
- **SVN Client** (svn command-line tools)
- **Ollama Server** (for AI documentation generation)
- **SMTP Server** (for email notifications)

## Core Components

### Services

- **Monitor Service**: Core SVN monitoring and change detection
- **User Management Service**: User creation, roles, and repository associations with LDAP integration
- **Repository Discovery Service**: Automatic repository discovery from SVN servers
- **Email Service**: Intelligent email notifications with user targeting
- **Ollama Service**: AI-powered documentation generation
- **File Manager**: Document and email storage with repository organization
- **Web Interface**: Flask-based configuration and monitoring interface
- **LDAP Service**: Enterprise directory integration with automatic user synchronization
- **Change Request Service**: SQL database integration for change management systems

### Data Models

- **User**: Role-based user accounts with notification preferences
- **Repository**: SVN repository configurations with user associations
- **Config**: Central configuration management with persistence

### Web Interface

- **Dashboard**: System status and monitoring overview
- **Repository Management**: Add, configure, and monitor repositories
- **User Management**: Create and manage users with role-based access
- **Repository Discovery**: Scan SVN servers for available repositories
- **Configuration**: System-wide settings and preferences
- **Logs**: Monitoring activity and system logs

## Getting Help

- **Configuration Issues**: See [Configuration Guide](configuration.md)
- **Usage Questions**: Check [User Guide](usage.md)
- **Development**: Review [Development Guide](development.md)
- **API Integration**: Consult [API Reference](api.md)

## Documentation Structure

```text
docs/
├── index.md                                # This overview document
├── design.md                               # System architecture and design
├── configuration.md                        # Configuration guide and examples
├── usage.md                                # User guide and workflows
├── deployment.md                           # Deployment and infrastructure
├── development.md                          # Development and contribution guide
├── ENTERPRISE_FEATURES.md                  # Complete enterprise feature overview
├── CHANGE_REQUEST_SETUP.md                 # Change request integration setup
├── FIELD_MAPPING_REFERENCE.md              # Database field mapping reference guide
├── CHANGE_REQUEST_TESTING_INTEGRATION.md   # Change request testing framework
├── LDAP_TESTING.md                         # LDAP testing and development setup
├── TESTING_FRAMEWORK.md                    # Comprehensive testing documentation
├── ldap-integration.md                     # LDAP integration guide
├── features.md                             # Feature documentation
├── integration.md                          # External service integration
├── quick-start.md                          # Quick start guide
├── README.md                               # Original README (moved from root)
└── CHANGELOG.md                            # Detailed change log
```

## Version Information

- **Current Version**: 2.0.0
- **Architecture**: Modular service-oriented design
- **Python Version**: 3.8+
- **Docker Support**: Yes
- **Web Interface**: Modern responsive design
- **AI Integration**: Ollama-powered documentation

## License

This project is licensed under the MIT License. See the LICENSE file for details.

## Contributing

We welcome contributions! Please see the [Development Guide](development.md) for information on how to contribute to this project.
